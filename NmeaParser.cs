using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text.RegularExpressions;

namespace GpsDataProcessor
{
    /// <summary>
    /// 标准NMEA消息解析器
    /// </summary>
    public static class NmeaParser
    {
        private static readonly Regex NmeaRegex = new Regex(
            @"^\$([A-Z]{2})([A-Z]{3}),(.+)\*([0-9A-F]{2})$",
            RegexOptions.Compiled);

        /// <summary>
        /// 验证NMEA消息的校验和
        /// </summary>
        public static bool ValidateChecksum(string nmeaMessage)
        {
            if (string.IsNullOrEmpty(nmeaMessage) || !nmeaMessage.Contains("*"))
                return false;

            try
            {
                var parts = nmeaMessage.Split('*');
                if (parts.Length != 2) return false;

                var message = parts[0];
                var checksumStr = parts[1];

                // 计算校验和（从$后开始，到*前结束）
                byte checksum = 0;
                for (int i = 1; i < message.Length; i++)
                {
                    checksum ^= (byte)message[i];
                }

                var expectedChecksum = Convert.ToByte(checksumStr, 16);
                return checksum == expectedChecksum;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 解析GGA消息（全球定位系统定位数据）
        /// </summary>
        public static GpsPosition ParseGGA(string nmeaMessage)
        {
            if (!ValidateChecksum(nmeaMessage)) return null;

            var parts = nmeaMessage.Split(',');
            if (parts.Length < 15 || !parts[0].EndsWith("GGA")) return null;

            try
            {
                var position = new GpsPosition();

                // UTC时间 (HHMMSS.SSS)
                if (!string.IsNullOrEmpty(parts[1]))
                {
                    if (double.TryParse(parts[1], NumberStyles.Float, CultureInfo.InvariantCulture, out var utcTime))
                    {
                        var hours = (int)(utcTime / 10000);
                        var minutes = (int)((utcTime % 10000) / 100);
                        var seconds = utcTime % 100;
                        position.UtcTime = DateTime.Today.AddHours(hours).AddMinutes(minutes).AddSeconds(seconds);
                    }
                }

                // 纬度 (DDMM.MMMM)
                if (!string.IsNullOrEmpty(parts[2]) && !string.IsNullOrEmpty(parts[3]))
                {
                    if (double.TryParse(parts[2], NumberStyles.Float, CultureInfo.InvariantCulture, out var lat))
                    {
                        position.Latitude = ConvertToDecimalDegrees(lat, parts[3]);
                    }
                }

                // 经度 (DDDMM.MMMM)
                if (!string.IsNullOrEmpty(parts[4]) && !string.IsNullOrEmpty(parts[5]))
                {
                    if (double.TryParse(parts[4], NumberStyles.Float, CultureInfo.InvariantCulture, out var lon))
                    {
                        position.Longitude = ConvertToDecimalDegrees(lon, parts[5]);
                    }
                }

                // 定位质量指示符
                if (!string.IsNullOrEmpty(parts[6]))
                {
                    var quality = int.Parse(parts[6]);
                    position.IsValid = quality > 0; // 0=无效, 1=GPS, 2=DGPS
                }

                // 使用的卫星数量
                if (!string.IsNullOrEmpty(parts[7]))
                {
                    position.SatelliteCount = int.Parse(parts[7]);
                }

                // 水平精度因子
                if (!string.IsNullOrEmpty(parts[8]))
                {
                    position.Hdop = double.Parse(parts[8], CultureInfo.InvariantCulture);
                }

                // 海拔高度
                if (!string.IsNullOrEmpty(parts[9]))
                {
                    position.Altitude = double.Parse(parts[9], CultureInfo.InvariantCulture);
                }

                return position;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 解析RMC消息（推荐最小GPS数据）
        /// </summary>
        public static GpsPosition ParseRMC(string nmeaMessage)
        {
            if (!ValidateChecksum(nmeaMessage)) return null;

            var parts = nmeaMessage.Split(',');
            if (parts.Length < 12 || !parts[0].EndsWith("RMC")) return null;

            try
            {
                var position = new GpsPosition();

                // UTC时间
                if (!string.IsNullOrEmpty(parts[1]))
                {
                    if (double.TryParse(parts[1], NumberStyles.Float, CultureInfo.InvariantCulture, out var utcTime))
                    {
                        var hours = (int)(utcTime / 10000);
                        var minutes = (int)((utcTime % 10000) / 100);
                        var seconds = utcTime % 100;
                        position.UtcTime = DateTime.Today.AddHours(hours).AddMinutes(minutes).AddSeconds(seconds);
                    }
                }

                // 状态 A=有效, V=无效
                position.IsValid = parts[2] == "A";

                // 纬度
                if (!string.IsNullOrEmpty(parts[3]) && !string.IsNullOrEmpty(parts[4]))
                {
                    if (double.TryParse(parts[3], NumberStyles.Float, CultureInfo.InvariantCulture, out var lat))
                    {
                        position.Latitude = ConvertToDecimalDegrees(lat, parts[4]);
                    }
                }

                // 经度
                if (!string.IsNullOrEmpty(parts[5]) && !string.IsNullOrEmpty(parts[6]))
                {
                    if (double.TryParse(parts[5], NumberStyles.Float, CultureInfo.InvariantCulture, out var lon))
                    {
                        position.Longitude = ConvertToDecimalDegrees(lon, parts[6]);
                    }
                }

                return position;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 解析GSV消息（可见GPS卫星信息）
        /// </summary>
        public static List<SatelliteInfo> ParseGSV(string nmeaMessage)
        {
            var satellites = new List<SatelliteInfo>();
            
            if (!ValidateChecksum(nmeaMessage)) return satellites;

            var parts = nmeaMessage.Split(',');
            if (parts.Length < 8 || !parts[0].EndsWith("GSV")) return satellites;

            try
            {
                // GSV消息格式：每4个字段描述一颗卫星
                for (int i = 4; i < parts.Length - 1; i += 4)
                {
                    if (i + 3 < parts.Length)
                    {
                        var satellite = new SatelliteInfo();
                        
                        // 卫星编号
                        if (!string.IsNullOrEmpty(parts[i]) && int.TryParse(parts[i], out var satId))
                            satellite.SatelliteId = satId;
                        
                        // 仰角
                        if (!string.IsNullOrEmpty(parts[i + 1]) && int.TryParse(parts[i + 1], out var elevation))
                            satellite.Elevation = elevation;
                        
                        // 方位角
                        if (!string.IsNullOrEmpty(parts[i + 2]) && int.TryParse(parts[i + 2], out var azimuth))
                            satellite.Azimuth = azimuth;
                        
                        // 信噪比
                        if (!string.IsNullOrEmpty(parts[i + 3]) && int.TryParse(parts[i + 3], out var signal))
                            satellite.SignalStrength = signal;
                        
                        if (satellite.SatelliteId > 0)
                            satellites.Add(satellite);
                    }
                }
            }
            catch
            {
                // 忽略解析错误
            }

            return satellites;
        }

        /// <summary>
        /// 解析GSA消息（GPS DOP和活动卫星）
        /// </summary>
        public static GpsPosition ParseGSA(string nmeaMessage)
        {
            if (!ValidateChecksum(nmeaMessage)) return null;

            var parts = nmeaMessage.Split(',');
            if (parts.Length < 18 || !parts[0].EndsWith("GSA")) return null;

            try
            {
                var position = new GpsPosition();

                // 定位模式 A=自动, M=手动
                // 定位类型 1=无定位, 2=2D定位, 3=3D定位
                if (!string.IsNullOrEmpty(parts[2]))
                {
                    var fixType = int.Parse(parts[2]);
                    position.IsValid = fixType >= 2;
                }

                // PDOP (位置精度因子)
                if (!string.IsNullOrEmpty(parts[15]))
                {
                    // 这里可以存储PDOP值，但GpsPosition类中没有对应字段
                }

                // HDOP (水平精度因子)
                if (!string.IsNullOrEmpty(parts[16]))
                {
                    position.Hdop = double.Parse(parts[16], CultureInfo.InvariantCulture);
                }

                // VDOP (垂直精度因子)
                if (!string.IsNullOrEmpty(parts[17]))
                {
                    // 这里可以存储VDOP值，但GpsPosition类中没有对应字段
                }

                return position;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 将度分格式转换为十进制度
        /// </summary>
        private static double ConvertToDecimalDegrees(double degreeMinutes, string direction)
        {
            var degrees = (int)(degreeMinutes / 100);
            var minutes = degreeMinutes % 100;
            var result = degrees + minutes / 60.0;
            
            if (direction == "S" || direction == "W")
                result = -result;
                
            return result;
        }

        /// <summary>
        /// 获取NMEA消息类型
        /// </summary>
        public static string GetMessageType(string nmeaMessage)
        {
            if (string.IsNullOrEmpty(nmeaMessage) || !nmeaMessage.StartsWith("$"))
                return null;

            var match = NmeaRegex.Match(nmeaMessage);
            if (match.Success)
            {
                return match.Groups[2].Value; // 返回消息类型 (如GGA, RMC, GSV等)
            }

            return null;
        }

        /// <summary>
        /// 获取NMEA消息的发送者ID
        /// </summary>
        public static string GetTalkerId(string nmeaMessage)
        {
            if (string.IsNullOrEmpty(nmeaMessage) || !nmeaMessage.StartsWith("$"))
                return null;

            var match = NmeaRegex.Match(nmeaMessage);
            if (match.Success)
            {
                return match.Groups[1].Value; // 返回发送者ID (如GP, GL, GB等)
            }

            return null;
        }
    }
}
