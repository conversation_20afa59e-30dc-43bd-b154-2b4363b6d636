using System;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using System.Linq;

namespace GpsDataProcessor
{
    /// <summary>
    /// GPS数据组，包含时间戳和相关的NMEA数据
    /// </summary>
    public class GpsDataGroup
    {
        public DateTime Timestamp { get; set; }
        public List<string> NmeaMessages { get; set; } = new List<string>();
        public string RawTimestamp { get; set; }
        
        // 解析后的关键信息
        public GpsPosition Position { get; set; }
        public List<SatelliteInfo> Satellites { get; set; } = new List<SatelliteInfo>();
    }

    /// <summary>
    /// GPS位置信息
    /// </summary>
    public class GpsPosition
    {
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public double Altitude { get; set; }
        public int SatelliteCount { get; set; }
        public double Hdop { get; set; }
        public DateTime UtcTime { get; set; }
        public bool IsValid { get; set; }
    }

    /// <summary>
    /// 卫星信息
    /// </summary>
    public class SatelliteInfo
    {
        public int SatelliteId { get; set; }
        public int Elevation { get; set; }
        public int Azimuth { get; set; }
        public int SignalStrength { get; set; }
    }

    /// <summary>
    /// GPS数据提取器
    /// </summary>
    public class GpsDataExtractor
    {
        // 时间戳正则表达式
        private static readonly Regex TimestampRegex = new Regex(
            @"\[(\d{2}:\d{2}:\d{2}\.\d{3})\]收←◆",
            RegexOptions.Compiled);

        // NMEA消息正则表达式
        private static readonly Regex NmeaRegex = new Regex(
            @"\+DGNSS:\s*""SPP"",""(\$[^""]+)""",
            RegexOptions.Compiled);

        /// <summary>
        /// 从文件中提取所有GPS数据组
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>GPS数据组列表</returns>
        public static List<GpsDataGroup> ExtractGpsDataGroups(string filePath)
        {
            var dataGroups = new List<GpsDataGroup>();
            var lines = File.ReadAllLines(filePath);
            
            GpsDataGroup currentGroup = null;
            
            foreach (var line in lines)
            {
                // 检查是否是新的时间戳行
                var timestampMatch = TimestampRegex.Match(line);
                if (timestampMatch.Success)
                {
                    // 如果有当前组，先添加到结果中
                    if (currentGroup != null && currentGroup.NmeaMessages.Count > 0)
                    {
                        ParseGpsData(currentGroup);
                        dataGroups.Add(currentGroup);
                    }
                    
                    // 创建新的数据组
                    currentGroup = new GpsDataGroup
                    {
                        RawTimestamp = timestampMatch.Groups[1].Value,
                        Timestamp = ParseTimestamp(timestampMatch.Groups[1].Value)
                    };
                }
                else if (currentGroup != null)
                {
                    // 检查是否是NMEA消息
                    var nmeaMatch = NmeaRegex.Match(line);
                    if (nmeaMatch.Success)
                    {
                        currentGroup.NmeaMessages.Add(nmeaMatch.Groups[1].Value);
                    }
                }
            }
            
            // 添加最后一组数据
            if (currentGroup != null && currentGroup.NmeaMessages.Count > 0)
            {
                ParseGpsData(currentGroup);
                dataGroups.Add(currentGroup);
            }
            
            return dataGroups;
        }

        /// <summary>
        /// 解析时间戳
        /// </summary>
        private static DateTime ParseTimestamp(string timeStr)
        {
            if (TimeSpan.TryParse(timeStr, out var timeSpan))
            {
                return DateTime.Today.Add(timeSpan);
            }
            return DateTime.MinValue;
        }

        /// <summary>
        /// 解析GPS数据组中的NMEA消息
        /// </summary>
        private static void ParseGpsData(GpsDataGroup group)
        {
            foreach (var nmea in group.NmeaMessages)
            {
                if (nmea.StartsWith("$GBGGA"))
                {
                    group.Position = ParseGGA(nmea);
                }
                else if (nmea.StartsWith("$GBGSV"))
                {
                    var satellites = ParseGSV(nmea);
                    group.Satellites.AddRange(satellites);
                }
            }
        }

        /// <summary>
        /// 解析GGA消息（位置信息）
        /// </summary>
        private static GpsPosition ParseGGA(string gga)
        {
            var parts = gga.Split(',');
            if (parts.Length < 15) return null;

            try
            {
                var position = new GpsPosition();
                
                // UTC时间
                if (double.TryParse(parts[1], out var utcTime))
                {
                    var hours = (int)(utcTime / 10000);
                    var minutes = (int)((utcTime % 10000) / 100);
                    var seconds = utcTime % 100;
                    position.UtcTime = DateTime.Today.AddHours(hours).AddMinutes(minutes).AddSeconds(seconds);
                }

                // 纬度
                if (double.TryParse(parts[2], out var lat))
                {
                    position.Latitude = ConvertToDecimalDegrees(lat, parts[3]);
                }

                // 经度
                if (double.TryParse(parts[4], out var lon))
                {
                    position.Longitude = ConvertToDecimalDegrees(lon, parts[5]);
                }

                // 定位质量
                position.IsValid = parts[6] == "1" || parts[6] == "2";

                // 卫星数量
                if (int.TryParse(parts[7], out var satCount))
                {
                    position.SatelliteCount = satCount;
                }

                // 水平精度因子
                if (double.TryParse(parts[8], out var hdop))
                {
                    position.Hdop = hdop;
                }

                // 海拔高度
                if (double.TryParse(parts[9], out var altitude))
                {
                    position.Altitude = altitude;
                }

                return position;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 解析GSV消息（卫星信息）
        /// </summary>
        private static List<SatelliteInfo> ParseGSV(string gsv)
        {
            var satellites = new List<SatelliteInfo>();
            var parts = gsv.Split(',');
            
            if (parts.Length < 8) return satellites;

            try
            {
                // GSV消息格式：每4个字段描述一颗卫星
                for (int i = 4; i < parts.Length - 1; i += 4)
                {
                    if (i + 3 < parts.Length)
                    {
                        var satellite = new SatelliteInfo();
                        
                        if (int.TryParse(parts[i], out var satId))
                            satellite.SatelliteId = satId;
                        
                        if (int.TryParse(parts[i + 1], out var elevation))
                            satellite.Elevation = elevation;
                        
                        if (int.TryParse(parts[i + 2], out var azimuth))
                            satellite.Azimuth = azimuth;
                        
                        if (int.TryParse(parts[i + 3], out var signal))
                            satellite.SignalStrength = signal;
                        
                        if (satellite.SatelliteId > 0)
                            satellites.Add(satellite);
                    }
                }
            }
            catch
            {
                // 忽略解析错误
            }

            return satellites;
        }

        /// <summary>
        /// 将度分格式转换为十进制度
        /// </summary>
        private static double ConvertToDecimalDegrees(double degreeMinutes, string direction)
        {
            var degrees = (int)(degreeMinutes / 100);
            var minutes = degreeMinutes % 100;
            var result = degrees + minutes / 60.0;
            
            if (direction == "S" || direction == "W")
                result = -result;
                
            return result;
        }
    }
}
