using System;
using System.Linq;

namespace GpsDataProcessor
{
    class Program
    {
        static void Main(string[] args)
        {
            try
            {
                // 提取GPS数据
                var dataGroups = GpsDataExtractor.ExtractGpsDataGroups("定位Log.txt");
                
                Console.WriteLine($"成功提取 {dataGroups.Count} 组GPS数据");
                Console.WriteLine();

                // 显示前5组数据的详细信息
                for (int i = 0; i < Math.Min(5, dataGroups.Count); i++)
                {
                    var group = dataGroups[i];
                    Console.WriteLine($"=== 第 {i + 1} 组数据 ===");
                    Console.WriteLine($"时间戳: {group.RawTimestamp}");
                    Console.WriteLine($"NMEA消息数量: {group.NmeaMessages.Count}");
                    
                    if (group.Position != null)
                    {
                        Console.WriteLine($"位置信息:");
                        Console.WriteLine($"  纬度: {group.Position.Latitude:F8}°");
                        Console.WriteLine($"  经度: {group.Position.Longitude:F8}°");
                        Console.WriteLine($"  海拔: {group.Position.Altitude:F3}m");
                        Console.WriteLine($"  卫星数量: {group.Position.SatelliteCount}");
                        Console.WriteLine($"  水平精度: {group.Position.Hdop:F2}");
                        Console.WriteLine($"  UTC时间: {group.Position.UtcTime:HH:mm:ss}");
                        Console.WriteLine($"  定位有效: {(group.Position.IsValid ? "是" : "否")}");
                    }
                    
                    if (group.Satellites.Count > 0)
                    {
                        Console.WriteLine($"卫星信息: {group.Satellites.Count} 颗可见卫星");
                        var strongSatellites = group.Satellites.Where(s => s.SignalStrength > 40).ToList();
                        Console.WriteLine($"  强信号卫星(>40dB): {strongSatellites.Count} 颗");
                    }
                    
                    Console.WriteLine();
                }

                // 统计信息
                Console.WriteLine("=== 统计信息 ===");
                var validPositions = dataGroups.Where(g => g.Position?.IsValid == true).ToList();
                Console.WriteLine($"有效定位数据组: {validPositions.Count}/{dataGroups.Count}");
                
                if (validPositions.Count > 0)
                {
                    var avgLat = validPositions.Average(g => g.Position.Latitude);
                    var avgLon = validPositions.Average(g => g.Position.Longitude);
                    var avgAlt = validPositions.Average(g => g.Position.Altitude);
                    var avgSatCount = validPositions.Average(g => g.Position.SatelliteCount);
                    var avgHdop = validPositions.Average(g => g.Position.Hdop);
                    
                    Console.WriteLine($"平均位置: {avgLat:F8}°N, {avgLon:F8}°E");
                    Console.WriteLine($"平均海拔: {avgAlt:F3}m");
                    Console.WriteLine($"平均卫星数量: {avgSatCount:F1}");
                    Console.WriteLine($"平均水平精度: {avgHdop:F3}");
                    
                    // 位置变化范围
                    var latRange = validPositions.Max(g => g.Position.Latitude) - validPositions.Min(g => g.Position.Latitude);
                    var lonRange = validPositions.Max(g => g.Position.Longitude) - validPositions.Min(g => g.Position.Longitude);
                    Console.WriteLine($"位置变化范围: 纬度±{latRange * 60:F3}', 经度±{lonRange * 60:F3}'");
                }

                // 导出到CSV文件
                ExportToCsv(dataGroups, "gps_data.csv");
                Console.WriteLine("\n数据已导出到 gps_data.csv 文件");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 导出数据到CSV文件
        /// </summary>
        static void ExportToCsv(List<GpsDataGroup> dataGroups, string fileName)
        {
            using (var writer = new System.IO.StreamWriter(fileName, false, System.Text.Encoding.UTF8))
            {
                // 写入标题行
                writer.WriteLine("时间戳,纬度,经度,海拔,卫星数量,水平精度,UTC时间,定位有效,可见卫星数,NMEA消息数");
                
                foreach (var group in dataGroups)
                {
                    var pos = group.Position;
                    writer.WriteLine($"{group.RawTimestamp}," +
                                   $"{pos?.Latitude:F8}," +
                                   $"{pos?.Longitude:F8}," +
                                   $"{pos?.Altitude:F3}," +
                                   $"{pos?.SatelliteCount}," +
                                   $"{pos?.Hdop:F3}," +
                                   $"{pos?.UtcTime:HH:mm:ss}," +
                                   $"{(pos?.IsValid == true ? "是" : "否")}," +
                                   $"{group.Satellites.Count}," +
                                   $"{group.NmeaMessages.Count}");
                }
            }
        }
    }
}
