using System;
using System.Threading;

namespace GpsDataProcessor
{
    /// <summary>
    /// 简单的GPS数据处理测试
    /// </summary>
    class SimpleGpsTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("纯净GPS数据流处理测试");
            Console.WriteLine("====================");
            
            // 创建实时处理器
            var processor = new RealTimeGpsProcessor();
            
            // 订阅事件
            processor.OnDataGroupComplete += (dataGroup) =>
            {
                Console.WriteLine($"\n[数据组] {dataGroup.Timestamp:HH:mm:ss.fff}");
                Console.WriteLine($"NMEA消息数: {dataGroup.NmeaMessages.Count}");
                
                if (dataGroup.Position?.IsValid == true)
                {
                    var pos = dataGroup.Position;
                    Console.WriteLine($"位置: {pos.Latitude:F8}°N, {pos.Longitude:F8}°E");
                    Console.WriteLine($"高度: {pos.Altitude:F1}m, 精度: {pos.Hdop:F2}");
                    Console.WriteLine($"卫星数: {pos.SatelliteCount}, UTC: {pos.UtcTime:HH:mm:ss}");
                }
                
                if (dataGroup.Satellites.Count > 0)
                {
                    var strongSats = dataGroup.Satellites.FindAll(s => s.SignalStrength > 40);
                    Console.WriteLine($"可见卫星: {dataGroup.Satellites.Count}, 强信号: {strongSats.Count}");
                }
            };
            
            processor.OnNmeaMessage += (nmea) =>
            {
                var messageType = NmeaParser.GetMessageType(nmea);
                var talkerId = NmeaParser.GetTalkerId(nmea);
                Console.WriteLine($"NMEA: {talkerId}{messageType} - {nmea.Substring(0, Math.Min(50, nmea.Length))}...");
            };
            
            processor.OnError += (error) =>
            {
                Console.WriteLine($"错误: {error}");
            };
            
            // 模拟纯净的GPS数据流
            Console.WriteLine("\n开始模拟GPS数据流...\n");
            
            // 这些是标准的NMEA消息，没有任何时间戳标识
            string[] nmeaMessages = {
                "$GPGGA,123519,4807.038,N,01131.000,E,1,08,0.9,545.4,M,46.9,M,,*47",
                "$GPGSA,A,3,04,05,,09,12,,,24,,,,,2.5,1.3,2.1*39",
                "$GPGSV,2,1,08,01,40,083,46,02,17,308,41,12,07,344,39,14,22,228,45*75",
                "$GPGSV,2,2,08,18,09,113,41,19,28,056,44,24,25,173,44,32,11,239,41*75",
                "$GPRMC,123519,A,4807.038,N,01131.000,E,022.4,084.4,230394,003.1,W*6A",
                
                "$GPGGA,123520,4807.039,N,01131.001,E,1,08,0.9,545.5,M,46.9,M,,*46",
                "$GPGSA,A,3,04,05,,09,12,,,24,,,,,2.5,1.3,2.1*39",
                "$GPGSV,2,1,08,01,40,083,47,02,17,308,42,12,07,344,40,14,22,228,46*74",
                "$GPGSV,2,2,08,18,09,113,42,19,28,056,45,24,25,173,45,32,11,239,42*74",
                "$GPRMC,123520,A,4807.039,N,01131.001,E,022.5,084.5,230394,003.1,W*69",
                
                "$GPGGA,123521,4807.040,N,01131.002,E,1,08,0.9,545.6,M,46.9,M,,*45",
                "$GPGSA,A,3,04,05,,09,12,,,24,,,,,2.5,1.3,2.1*39",
                "$GPGSV,2,1,08,01,40,083,48,02,17,308,43,12,07,344,41,14,22,228,47*73",
                "$GPGSV,2,2,08,18,09,113,43,19,28,056,46,24,25,173,46,32,11,239,43*73",
                "$GPRMC,123521,A,4807.040,N,01131.002,E,022.6,084.6,230394,003.1,W*68"
            };
            
            // 模拟实时数据接收
            foreach (var nmea in nmeaMessages)
            {
                // 验证NMEA消息
                if (NmeaParser.ValidateChecksum(nmea))
                {
                    processor.ProcessData(nmea + "\r\n");
                }
                else
                {
                    Console.WriteLine($"校验和错误: {nmea}");
                }
                
                Thread.Sleep(200); // 模拟数据接收间隔
            }
            
            // 确保处理完所有数据
            processor.Flush();
            
            Console.WriteLine("\n测试完成！");
            Console.WriteLine("\n实际应用中，您可以：");
            Console.WriteLine("1. 从串口接收GPS数据：processor.ProcessData(serialPort.ReadExisting())");
            Console.WriteLine("2. 从网络接收GPS数据：processor.ProcessData(networkStream.Read())");
            Console.WriteLine("3. 从文件读取GPS数据：processor.ProcessData(fileContent)");
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
    
    /// <summary>
    /// 串口GPS数据接收示例
    /// </summary>
    class SerialPortGpsExample
    {
        public static void RunSerialPortExample()
        {
            var processor = new RealTimeGpsProcessor();
            
            // 设置事件处理
            processor.OnDataGroupComplete += (dataGroup) =>
            {
                Console.WriteLine($"GPS数据组: {dataGroup.NmeaMessages.Count} 条消息");
                if (dataGroup.Position?.IsValid == true)
                {
                    Console.WriteLine($"位置: {dataGroup.Position.Latitude:F6}, {dataGroup.Position.Longitude:F6}");
                }
            };
            
            // 串口设置示例
            try
            {
                using (var serialPort = new System.IO.Ports.SerialPort("COM3", 9600))
                {
                    serialPort.DataReceived += (sender, e) =>
                    {
                        var port = (System.IO.Ports.SerialPort)sender;
                        var data = port.ReadExisting();
                        processor.ProcessData(data);
                    };
                    
                    serialPort.Open();
                    Console.WriteLine("串口已打开，正在接收GPS数据...");
                    
                    // 保持程序运行
                    Console.ReadKey();
                    
                    serialPort.Close();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"串口操作失败: {ex.Message}");
            }
            
            processor.Flush();
        }
    }
    
    /// <summary>
    /// 网络GPS数据接收示例
    /// </summary>
    class NetworkGpsExample
    {
        public static async System.Threading.Tasks.Task RunNetworkExample()
        {
            var processor = new RealTimeGpsProcessor();
            
            processor.OnDataGroupComplete += (dataGroup) =>
            {
                Console.WriteLine($"网络GPS数据: {dataGroup.NmeaMessages.Count} 条消息");
            };
            
            try
            {
                using (var client = new System.Net.Sockets.TcpClient())
                {
                    await client.ConnectAsync("gps-server.example.com", 2947); // GPSD默认端口
                    
                    using (var stream = client.GetStream())
                    {
                        var buffer = new byte[1024];
                        
                        while (client.Connected)
                        {
                            var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                            if (bytesRead > 0)
                            {
                                var data = System.Text.Encoding.ASCII.GetString(buffer, 0, bytesRead);
                                processor.ProcessData(data);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"网络连接失败: {ex.Message}");
            }
            
            processor.Flush();
        }
    }
}
