using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace GpsDataProcessor
{
    /// <summary>
    /// 纯净GPS数据流处理器 - 专门处理没有时间戳标识的原始GPS数据
    /// </summary>
    public class PureGpsStreamProcessor
    {
        private readonly RealTimeGpsProcessor _processor;
        private SerialPort _serialPort;
        private bool _isRunning = false;

        public PureGpsStreamProcessor()
        {
            _processor = new RealTimeGpsProcessor();
            SetupEventHandlers();
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            _processor.OnDataGroupComplete += OnGpsDataGroupReceived;
            _processor.OnNmeaMessage += OnNmeaMessageReceived;
            _processor.OnError += OnProcessingError;
        }

        /// <summary>
        /// 从串口接收GPS数据
        /// </summary>
        /// <param name="portName">串口名称，如"COM3"</param>
        /// <param name="baudRate">波特率，通常是9600或115200</param>
        public bool StartSerialPortReception(string portName, int baudRate = 9600)
        {
            try
            {
                _serialPort = new SerialPort(portName, baudRate, Parity.None, 8, StopBits.One)
                {
                    ReadTimeout = 1000,
                    WriteTimeout = 1000
                };

                _serialPort.DataReceived += SerialPort_DataReceived;
                _serialPort.Open();
                _isRunning = true;

                Console.WriteLine($"已连接到串口 {portName}，波特率 {baudRate}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"串口连接失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 串口数据接收事件处理
        /// </summary>
        private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                var port = (SerialPort)sender;
                var data = port.ReadExisting();
                
                // 处理接收到的原始GPS数据
                _processor.ProcessData(data);
            }
            catch (Exception ex)
            {
                OnProcessingError($"串口数据接收错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止串口接收
        /// </summary>
        public void StopSerialPortReception()
        {
            _isRunning = false;
            
            if (_serialPort?.IsOpen == true)
            {
                _serialPort.Close();
                _serialPort.Dispose();
                _serialPort = null;
            }
            
            _processor.Flush();
            Console.WriteLine("串口连接已关闭");
        }

        /// <summary>
        /// 从网络流接收GPS数据
        /// </summary>
        public async Task StartNetworkReception(string host, int port)
        {
            try
            {
                using (var client = new System.Net.Sockets.TcpClient())
                {
                    await client.ConnectAsync(host, port);
                    Console.WriteLine($"已连接到网络GPS源 {host}:{port}");
                    
                    using (var stream = client.GetStream())
                    {
                        var buffer = new byte[1024];
                        _isRunning = true;
                        
                        while (_isRunning && client.Connected)
                        {
                            var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                            if (bytesRead > 0)
                            {
                                var data = Encoding.ASCII.GetString(buffer, 0, bytesRead);
                                _processor.ProcessData(data);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                OnProcessingError($"网络接收错误: {ex.Message}");
            }
            finally
            {
                _processor.Flush();
                Console.WriteLine("网络连接已关闭");
            }
        }

        /// <summary>
        /// 模拟处理原始GPS数据（从日志文件提取纯净数据）
        /// </summary>
        public void ProcessRawGpsFromLog(string logFilePath)
        {
            try
            {
                var lines = System.IO.File.ReadAllLines(logFilePath);
                var rawGpsData = new StringBuilder();
                
                foreach (var line in lines)
                {
                    // 提取纯净的NMEA数据
                    if (line.Contains("+DGNSS: \"SPP\",\"$"))
                    {
                        var start = line.IndexOf("\"$");
                        var end = line.LastIndexOf("\"");
                        if (start >= 0 && end > start)
                        {
                            var nmeaMessage = line.Substring(start + 1, end - start - 1);
                            rawGpsData.AppendLine(nmeaMessage);
                        }
                    }
                }
                
                Console.WriteLine("开始处理提取的纯净GPS数据...");
                
                // 模拟实时数据流
                var gpsLines = rawGpsData.ToString().Split('\n');
                foreach (var gpsLine in gpsLines)
                {
                    if (!string.IsNullOrWhiteSpace(gpsLine))
                    {
                        _processor.ProcessData(gpsLine + "\r\n");
                        Thread.Sleep(100); // 模拟数据接收间隔
                    }
                }
                
                _processor.Flush();
                Console.WriteLine("GPS数据处理完成");
            }
            catch (Exception ex)
            {
                OnProcessingError($"处理日志文件错误: {ex.Message}");
            }
        }

        /// <summary>
        /// GPS数据组接收完成事件
        /// </summary>
        private void OnGpsDataGroupReceived(GpsDataGroup dataGroup)
        {
            Console.WriteLine($"[{dataGroup.Timestamp:HH:mm:ss.fff}] 接收到GPS数据组:");
            Console.WriteLine($"  NMEA消息数: {dataGroup.NmeaMessages.Count}");
            
            if (dataGroup.Position?.IsValid == true)
            {
                var pos = dataGroup.Position;
                Console.WriteLine($"  位置: {pos.Latitude:F8}°N, {pos.Longitude:F8}°E");
                Console.WriteLine($"  高度: {pos.Altitude:F1}m, 精度: {pos.Hdop:F2}");
                Console.WriteLine($"  卫星数: {pos.SatelliteCount}, UTC: {pos.UtcTime:HH:mm:ss}");
            }
            
            if (dataGroup.Satellites.Count > 0)
            {
                var strongSats = dataGroup.Satellites.FindAll(s => s.SignalStrength > 40);
                Console.WriteLine($"  可见卫星: {dataGroup.Satellites.Count}, 强信号: {strongSats.Count}");
            }
            
            Console.WriteLine();
        }

        /// <summary>
        /// 单个NMEA消息接收事件
        /// </summary>
        private void OnNmeaMessageReceived(string nmeaMessage)
        {
            // 可以在这里进行实时的单个消息处理
            // Console.WriteLine($"NMEA: {nmeaMessage}");
        }

        /// <summary>
        /// 处理错误事件
        /// </summary>
        private void OnProcessingError(string error)
        {
            Console.WriteLine($"错误: {error}");
        }

        /// <summary>
        /// 获取可用的串口列表
        /// </summary>
        public static string[] GetAvailableSerialPorts()
        {
            return SerialPort.GetPortNames();
        }
    }

    /// <summary>
    /// 纯净GPS数据流处理示例程序
    /// </summary>
    class PureGpsStreamExample
    {
        static async Task Main(string[] args)
        {
            var processor = new PureGpsStreamProcessor();
            
            Console.WriteLine("纯净GPS数据流处理器");
            Console.WriteLine("1. 串口接收GPS数据");
            Console.WriteLine("2. 网络接收GPS数据");
            Console.WriteLine("3. 从日志文件提取纯净数据进行模拟");
            Console.Write("请选择数据源 (1-3): ");
            
            var choice = Console.ReadLine();
            
            switch (choice)
            {
                case "1":
                    await HandleSerialPortReception(processor);
                    break;
                case "2":
                    await HandleNetworkReception(processor);
                    break;
                case "3":
                    processor.ProcessRawGpsFromLog("定位Log.txt");
                    break;
                default:
                    Console.WriteLine("无效选择，使用日志文件模拟");
                    processor.ProcessRawGpsFromLog("定位Log.txt");
                    break;
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        static async Task HandleSerialPortReception(PureGpsStreamProcessor processor)
        {
            var ports = PureGpsStreamProcessor.GetAvailableSerialPorts();
            Console.WriteLine($"可用串口: {string.Join(", ", ports)}");
            Console.Write("请输入串口名称 (如COM3): ");
            var portName = Console.ReadLine();
            
            Console.Write("请输入波特率 (默认9600): ");
            var baudRateStr = Console.ReadLine();
            var baudRate = string.IsNullOrEmpty(baudRateStr) ? 9600 : int.Parse(baudRateStr);
            
            if (processor.StartSerialPortReception(portName, baudRate))
            {
                Console.WriteLine("正在接收GPS数据，按任意键停止...");
                Console.ReadKey();
                processor.StopSerialPortReception();
            }
        }

        static async Task HandleNetworkReception(PureGpsStreamProcessor processor)
        {
            Console.Write("请输入GPS服务器地址: ");
            var host = Console.ReadLine();
            Console.Write("请输入端口号: ");
            var port = int.Parse(Console.ReadLine());
            
            Console.WriteLine("正在连接GPS网络源...");
            await processor.StartNetworkReception(host, port);
        }
    }
}
