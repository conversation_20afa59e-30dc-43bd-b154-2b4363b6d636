using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Text;

namespace GpsDataProcessor
{
    /// <summary>
    /// 实时GPS数据处理器，用于处理纯净的GPS数据流（无时间戳标识）
    /// </summary>
    public class RealTimeGpsProcessor
    {
        private readonly StringBuilder _buffer = new StringBuilder();
        private readonly List<string> _currentNmeaMessages = new List<string>();
        private DateTime _lastGroupTime = DateTime.MinValue;
        private readonly TimeSpan _groupTimeout = TimeSpan.FromSeconds(2); // 数据组超时时间

        // 正则表达式 - 直接匹配NMEA消息
        private static readonly Regex NmeaRegex = new Regex(
            @"(\$[A-Z]{5},[^*]*\*[0-9A-F]{2})",
            RegexOptions.Compiled);

        // AT命令正则表达式
        private static readonly Regex AtCommandRegex = new Regex(
            @"(AT[^\\r\\n]*)",
            RegexOptions.Compiled);

        // 事件
        public event Action<GpsDataGroup> OnDataGroupComplete;
        public event Action<string> OnError;
        public event Action<string> OnNmeaMessage; // 单个NMEA消息事件

        /// <summary>
        /// 处理新接收到的数据
        /// </summary>
        /// <param name="data">新数据</param>
        public void ProcessData(string data)
        {
            try
            {
                _buffer.Append(data);
                ProcessBuffer();
            }
            catch (Exception ex)
            {
                OnError?.Invoke($"处理数据时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理缓冲区中的数据
        /// </summary>
        private void ProcessBuffer()
        {
            var bufferContent = _buffer.ToString();

            // 查找所有完整的NMEA消息
            var matches = NmeaRegex.Matches(bufferContent);
            int lastMatchEnd = 0;

            foreach (Match match in matches)
            {
                var nmeaMessage = match.Groups[1].Value;
                ProcessNmeaMessage(nmeaMessage);
                lastMatchEnd = match.Index + match.Length;
            }

            // 检查数据组超时
            CheckGroupTimeout();

            // 保留未处理的数据
            if (lastMatchEnd > 0)
            {
                var remaining = bufferContent.Substring(lastMatchEnd);
                _buffer.Clear();
                _buffer.Append(remaining);
            }
        }

        /// <summary>
        /// 处理单个NMEA消息
        /// </summary>
        private void ProcessNmeaMessage(string nmeaMessage)
        {
            try
            {
                // 触发单个NMEA消息事件
                OnNmeaMessage?.Invoke(nmeaMessage);

                // 添加到当前数据组
                _currentNmeaMessages.Add(nmeaMessage);
                _lastGroupTime = DateTime.Now;

                // 检查是否是数据组的结束标志
                // 通常GGA消息标志着一个完整数据组的开始，RMC消息标志着结束
                if (nmeaMessage.StartsWith("$GBRMC") || nmeaMessage.StartsWith("$GNRMC"))
                {
                    CompleteCurrentDataGroup();
                }
                // 如果收集到足够多的消息，也可以触发数据组完成
                else if (_currentNmeaMessages.Count >= 10) // 可配置的阈值
                {
                    CompleteCurrentDataGroup();
                }
            }
            catch (Exception ex)
            {
                OnError?.Invoke($"处理NMEA消息时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查数据组超时
        /// </summary>
        private void CheckGroupTimeout()
        {
            if (_currentNmeaMessages.Count > 0 &&
                _lastGroupTime != DateTime.MinValue &&
                DateTime.Now - _lastGroupTime > _groupTimeout)
            {
                CompleteCurrentDataGroup();
            }
        }

        /// <summary>
        /// 完成当前数据组的处理
        /// </summary>
        private void CompleteCurrentDataGroup()
        {
            if (_currentNmeaMessages.Count == 0)
                return;

            try
            {
                var dataGroup = new GpsDataGroup
                {
                    RawTimestamp = DateTime.Now.ToString("HH:mm:ss.fff"),
                    Timestamp = DateTime.Now,
                    NmeaMessages = new List<string>(_currentNmeaMessages)
                };

                // 解析GPS数据
                ParseGpsData(dataGroup);

                // 触发事件
                OnDataGroupComplete?.Invoke(dataGroup);

                // 清空当前数据组
                _currentNmeaMessages.Clear();
                _lastGroupTime = DateTime.MinValue;
            }
            catch (Exception ex)
            {
                OnError?.Invoke($"完成数据组处理时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 强制完成当前数据组（用于流结束时）
        /// </summary>
        public void Flush()
        {
            if (_currentNmeaMessages.Count > 0)
            {
                CompleteCurrentDataGroup();
            }
            _buffer.Clear();
            _currentNmeaMessages.Clear();
            _lastGroupTime = DateTime.MinValue;
        }

        /// <summary>
        /// 设置数据组完成的触发条件
        /// </summary>
        public void SetGroupCompletionSettings(int maxMessagesPerGroup = 10, TimeSpan? groupTimeout = null)
        {
            // 这些设置可以根据实际GPS设备的数据特征进行调整
        }

        /// <summary>
        /// 解析GPS数据
        /// </summary>
        private static void ParseGpsData(GpsDataGroup group)
        {
            foreach (var nmea in group.NmeaMessages)
            {
                var messageType = NmeaParser.GetMessageType(nmea);

                switch (messageType)
                {
                    case "GGA":
                        var ggaPosition = NmeaParser.ParseGGA(nmea);
                        if (ggaPosition != null)
                        {
                            group.Position = ggaPosition;
                        }
                        break;

                    case "RMC":
                        var rmcPosition = NmeaParser.ParseRMC(nmea);
                        if (rmcPosition != null && group.Position == null)
                        {
                            group.Position = rmcPosition;
                        }
                        break;

                    case "GSV":
                        var satellites = NmeaParser.ParseGSV(nmea);
                        group.Satellites.AddRange(satellites);
                        break;

                    case "GSA":
                        var gsaPosition = NmeaParser.ParseGSA(nmea);
                        if (gsaPosition != null && group.Position != null)
                        {
                            // 合并GSA信息到现有位置信息
                            group.Position.Hdop = gsaPosition.Hdop;
                            if (!group.Position.IsValid)
                                group.Position.IsValid = gsaPosition.IsValid;
                        }
                        break;
                }
            }
        }



    }
}
