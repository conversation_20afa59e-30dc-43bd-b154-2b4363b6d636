using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Text;

namespace GpsDataProcessor
{
    /// <summary>
    /// 实时GPS数据处理器，用于处理流式数据
    /// </summary>
    public class RealTimeGpsProcessor
    {
        private readonly StringBuilder _buffer = new StringBuilder();
        private readonly List<string> _currentNmeaMessages = new List<string>();
        private string _currentTimestamp;
        private bool _inDataGroup = false;

        // 正则表达式
        private static readonly Regex TimestampRegex = new Regex(
            @"\[(\d{2}:\d{2}:\d{2}\.\d{3})\]收←◆",
            RegexOptions.Compiled);

        private static readonly Regex NmeaRegex = new Regex(
            @"\+DGNSS:\s*""SPP"",""(\$[^""]+)""",
            RegexOptions.Compiled);

        // 事件
        public event Action<GpsDataGroup> OnDataGroupComplete;
        public event Action<string> OnError;

        /// <summary>
        /// 处理新接收到的数据
        /// </summary>
        /// <param name="data">新数据</param>
        public void ProcessData(string data)
        {
            try
            {
                _buffer.Append(data);
                ProcessBuffer();
            }
            catch (Exception ex)
            {
                OnError?.Invoke($"处理数据时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理缓冲区中的数据
        /// </summary>
        private void ProcessBuffer()
        {
            var bufferContent = _buffer.ToString();
            var lines = bufferContent.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            
            // 保留最后一行（可能不完整）
            var completeLines = lines.Length > 1 ? lines[..^1] : new string[0];
            var lastLine = lines.Length > 0 ? lines[^1] : "";

            // 处理完整的行
            foreach (var line in completeLines)
            {
                ProcessLine(line);
            }

            // 更新缓冲区，只保留最后一行
            _buffer.Clear();
            _buffer.Append(lastLine);
        }

        /// <summary>
        /// 处理单行数据
        /// </summary>
        private void ProcessLine(string line)
        {
            // 检查时间戳
            var timestampMatch = TimestampRegex.Match(line);
            if (timestampMatch.Success)
            {
                // 完成当前数据组
                if (_inDataGroup && _currentNmeaMessages.Count > 0)
                {
                    CompleteCurrentDataGroup();
                }

                // 开始新的数据组
                _currentTimestamp = timestampMatch.Groups[1].Value;
                _currentNmeaMessages.Clear();
                _inDataGroup = true;
            }
            else if (_inDataGroup)
            {
                // 检查NMEA消息
                var nmeaMatch = NmeaRegex.Match(line);
                if (nmeaMatch.Success)
                {
                    _currentNmeaMessages.Add(nmeaMatch.Groups[1].Value);
                }
                // 检查是否是数据组结束的标志
                else if (string.IsNullOrWhiteSpace(line) || line.Contains("output:") || line.Contains("input:"))
                {
                    // 可能的数据组结束，但不立即处理，等待下一个时间戳
                }
            }
        }

        /// <summary>
        /// 完成当前数据组的处理
        /// </summary>
        private void CompleteCurrentDataGroup()
        {
            if (string.IsNullOrEmpty(_currentTimestamp) || _currentNmeaMessages.Count == 0)
                return;

            try
            {
                var dataGroup = new GpsDataGroup
                {
                    RawTimestamp = _currentTimestamp,
                    Timestamp = ParseTimestamp(_currentTimestamp),
                    NmeaMessages = new List<string>(_currentNmeaMessages)
                };

                // 解析GPS数据
                ParseGpsData(dataGroup);

                // 触发事件
                OnDataGroupComplete?.Invoke(dataGroup);
            }
            catch (Exception ex)
            {
                OnError?.Invoke($"完成数据组处理时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 强制完成当前数据组（用于流结束时）
        /// </summary>
        public void Flush()
        {
            if (_inDataGroup && _currentNmeaMessages.Count > 0)
            {
                CompleteCurrentDataGroup();
            }
            _buffer.Clear();
            _currentNmeaMessages.Clear();
            _inDataGroup = false;
        }

        /// <summary>
        /// 解析时间戳
        /// </summary>
        private static DateTime ParseTimestamp(string timeStr)
        {
            if (TimeSpan.TryParse(timeStr, out var timeSpan))
            {
                return DateTime.Today.Add(timeSpan);
            }
            return DateTime.MinValue;
        }

        /// <summary>
        /// 解析GPS数据
        /// </summary>
        private static void ParseGpsData(GpsDataGroup group)
        {
            foreach (var nmea in group.NmeaMessages)
            {
                if (nmea.StartsWith("$GBGGA"))
                {
                    group.Position = ParseGGA(nmea);
                }
                else if (nmea.StartsWith("$GBGSV"))
                {
                    var satellites = ParseGSV(nmea);
                    group.Satellites.AddRange(satellites);
                }
            }
        }

        /// <summary>
        /// 解析GGA消息
        /// </summary>
        private static GpsPosition ParseGGA(string gga)
        {
            var parts = gga.Split(',');
            if (parts.Length < 15) return null;

            try
            {
                var position = new GpsPosition();
                
                // UTC时间
                if (double.TryParse(parts[1], out var utcTime))
                {
                    var hours = (int)(utcTime / 10000);
                    var minutes = (int)((utcTime % 10000) / 100);
                    var seconds = utcTime % 100;
                    position.UtcTime = DateTime.Today.AddHours(hours).AddMinutes(minutes).AddSeconds(seconds);
                }

                // 纬度
                if (double.TryParse(parts[2], out var lat))
                {
                    position.Latitude = ConvertToDecimalDegrees(lat, parts[3]);
                }

                // 经度
                if (double.TryParse(parts[4], out var lon))
                {
                    position.Longitude = ConvertToDecimalDegrees(lon, parts[5]);
                }

                // 定位质量
                position.IsValid = parts[6] == "1" || parts[6] == "2";

                // 卫星数量
                if (int.TryParse(parts[7], out var satCount))
                {
                    position.SatelliteCount = satCount;
                }

                // 水平精度因子
                if (double.TryParse(parts[8], out var hdop))
                {
                    position.Hdop = hdop;
                }

                // 海拔高度
                if (double.TryParse(parts[9], out var altitude))
                {
                    position.Altitude = altitude;
                }

                return position;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 解析GSV消息
        /// </summary>
        private static List<SatelliteInfo> ParseGSV(string gsv)
        {
            var satellites = new List<SatelliteInfo>();
            var parts = gsv.Split(',');
            
            if (parts.Length < 8) return satellites;

            try
            {
                for (int i = 4; i < parts.Length - 1; i += 4)
                {
                    if (i + 3 < parts.Length)
                    {
                        var satellite = new SatelliteInfo();
                        
                        if (int.TryParse(parts[i], out var satId))
                            satellite.SatelliteId = satId;
                        
                        if (int.TryParse(parts[i + 1], out var elevation))
                            satellite.Elevation = elevation;
                        
                        if (int.TryParse(parts[i + 2], out var azimuth))
                            satellite.Azimuth = azimuth;
                        
                        if (int.TryParse(parts[i + 3], out var signal))
                            satellite.SignalStrength = signal;
                        
                        if (satellite.SatelliteId > 0)
                            satellites.Add(satellite);
                    }
                }
            }
            catch
            {
                // 忽略解析错误
            }

            return satellites;
        }

        /// <summary>
        /// 将度分格式转换为十进制度
        /// </summary>
        private static double ConvertToDecimalDegrees(double degreeMinutes, string direction)
        {
            var degrees = (int)(degreeMinutes / 100);
            var minutes = degreeMinutes % 100;
            var result = degrees + minutes / 60.0;
            
            if (direction == "S" || direction == "W")
                result = -result;
                
            return result;
        }
    }
}
