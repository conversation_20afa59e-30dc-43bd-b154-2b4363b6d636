using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace GpsDataProcessor
{
    /// <summary>
    /// 实时GPS数据处理示例
    /// </summary>
    public class RealTimeExample
    {
        public static async Task RunRealTimeProcessing()
        {
            var processor = new RealTimeGpsProcessor();
            int dataGroupCount = 0;

            // 订阅事件
            processor.OnDataGroupComplete += (dataGroup) =>
            {
                dataGroupCount++;
                Console.WriteLine($"[{dataGroupCount:D3}] {dataGroup.RawTimestamp} - " +
                                $"NMEA: {dataGroup.NmeaMessages.Count}, " +
                                $"位置: {(dataGroup.Position?.IsValid == true ? "有效" : "无效")}, " +
                                $"卫星: {dataGroup.Satellites.Count}");

                if (dataGroup.Position?.IsValid == true)
                {
                    Console.WriteLine($"     位置: {dataGroup.Position.Latitude:F6}°N, " +
                                    $"{dataGroup.Position.Longitude:F6}°E, " +
                                    $"高度: {dataGroup.Position.Altitude:F1}m");
                }
            };

            processor.OnError += (error) =>
            {
                Console.WriteLine($"错误: {error}");
            };

            Console.WriteLine("开始实时处理GPS数据...");
            Console.WriteLine("模拟从文件逐行读取数据（实际应用中可以是串口、网络等数据源）");
            Console.WriteLine();

            // 模拟实时数据流 - 从文件逐行读取
            try
            {
                using (var reader = new StreamReader("定位Log.txt"))
                {
                    string line;
                    while ((line = await reader.ReadLineAsync()) != null)
                    {
                        // 模拟数据接收延迟
                        await Task.Delay(10);
                        
                        // 处理数据
                        processor.ProcessData(line + "\n");
                    }
                }

                // 确保处理完所有数据
                processor.Flush();
                
                Console.WriteLine($"\n处理完成，共处理 {dataGroupCount} 组GPS数据");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"处理过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 模拟串口数据接收
        /// </summary>
        public static void SimulateSerialPortProcessing()
        {
            var processor = new RealTimeGpsProcessor();
            
            // 订阅事件
            processor.OnDataGroupComplete += (dataGroup) =>
            {
                Console.WriteLine($"接收到GPS数据组: {dataGroup.RawTimestamp}");
                if (dataGroup.Position?.IsValid == true)
                {
                    // 这里可以进行实时位置处理
                    ProcessRealTimePosition(dataGroup.Position);
                }
            };

            processor.OnError += (error) =>
            {
                Console.WriteLine($"GPS数据处理错误: {error}");
            };

            // 模拟串口数据接收
            Console.WriteLine("模拟串口数据接收...");
            
            // 这里应该是实际的串口数据接收代码
            // 例如：serialPort.DataReceived += (sender, e) => { processor.ProcessData(receivedData); };
            
            // 模拟一些数据
            string[] sampleData = {
                "[09:51:37.848]收←◆",
                "+DGNSS: \"SPP\",\"$GBGGA,040915.000,3954.3338465,N,11623.4820171,E,1,16,0.77,63.947,M,-8.163,M,,*57\"",
                "+DGNSS: \"SPP\",\"$GBGSA,A,3,02,05,04,03,01,09,29,20,07,28,26,19,1.41,0.77,1.18,4*04\"",
                "",
                "[09:51:38.842]收←◆",
                "+DGNSS: \"SPP\",\"$GBGGA,040916.000,3954.3338464,N,11623.4820299,E,1,16,0.77,63.995,M,-8.163,M,,*5F\""
            };

            foreach (var data in sampleData)
            {
                processor.ProcessData(data + "\n");
                Thread.Sleep(100); // 模拟数据接收间隔
            }

            processor.Flush();
        }

        /// <summary>
        /// 处理实时位置信息
        /// </summary>
        private static void ProcessRealTimePosition(GpsPosition position)
        {
            // 实时位置处理逻辑
            Console.WriteLine($"  实时位置更新: {position.Latitude:F8}°N, {position.Longitude:F8}°E");
            Console.WriteLine($"  精度: {position.Hdop:F2}, 卫星: {position.SatelliteCount}, 高度: {position.Altitude:F1}m");
            
            // 这里可以添加：
            // 1. 位置变化检测
            // 2. 地理围栏检查
            // 3. 轨迹记录
            // 4. 实时地图更新
            // 5. 位置数据存储
        }
    }

    /// <summary>
    /// 扩展的实时处理示例程序
    /// </summary>
    class ExtendedRealTimeProgram
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("GPS数据实时处理示例");
            Console.WriteLine("1. 文件模拟实时处理");
            Console.WriteLine("2. 串口模拟处理");
            Console.Write("请选择处理方式 (1 或 2): ");
            
            var choice = Console.ReadLine();
            
            switch (choice)
            {
                case "1":
                    await RealTimeExample.RunRealTimeProcessing();
                    break;
                case "2":
                    RealTimeExample.SimulateSerialPortProcessing();
                    break;
                default:
                    Console.WriteLine("无效选择，默认使用文件模拟处理");
                    await RealTimeExample.RunRealTimeProcessing();
                    break;
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
